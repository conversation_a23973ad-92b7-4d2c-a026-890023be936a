spring.application.name=file-transfer
server.port= 9090

spring.datasource.url=******************************************
spring.datasource.username=root
spring.datasource.password=123456789
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# Hibernate properties
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect


spring.servlet.multipart.max-file-size=2GB
spring.servlet.multipart.max-request-size=2GB

# Connection and timeout settings for video streaming
server.tomcat.connection-timeout=60000
server.tomcat.keep-alive-timeout=60000
server.tomcat.max-keep-alive-requests=100
spring.mvc.async.request-timeout=300000

# HTTP response settings
server.compression.enabled=true
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
server.compression.min-response-size=1024

# Tomcat thread pool settings for better concurrent handling
server.tomcat.threads.max=200
server.tomcat.threads.min-spare=10
server.tomcat.max-connections=8192

# Logging level for debugging connection issues (can be removed in production)
logging.level.org.springframework.web.context.request.async=INFO
logging.level.org.apache.coyote.http11=INFO