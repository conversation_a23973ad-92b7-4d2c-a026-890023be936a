<!DOCTYPE html>
<html
  xmlns:th="http://www.thymeleaf.org"
  xmlns:sec="http://www.thymeleaf.org/extras/spring-security"
  lang="vi"
>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script
      src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"
      defer
    ></script>
    <link rel="stylesheet" th:href="@{/css/input.css}" />
  </head>
  <body>
      <section class="gradient-bg min-h-screen flex items-center relative overflow-hidden" th:fragment="hero">
        <div class="absolute inset-0 opacity-20">
            <div class="absolute top-20 left-10 w-20 h-20 bg-white rounded-full float-animation"></div>
            <div class="absolute top-40 right-20 w-16 h-16 bg-purple-300 rounded-full float-animation" style="animation-delay: -2s;"></div>
            <div class="absolute bottom-40 left-1/4 w-12 h-12 bg-indigo-300 rounded-full float-animation" style="animation-delay: -4s;"></div>
        </div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div class="text-white">
                    <h1 class="text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                        Quản lý file
                        <span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-pink-400">
                            thông minh
                        </span>
                        như chưa từng có
                    </h1>
                    <p class="text-xl mb-8 text-purple-100" th:text="${heroDescription ?: 'Tổ chức, chia sẻ và bảo mật file của bạn với công nghệ AI tiên tiến. Trải nghiệm quản lý file thế hệ mới.'}">
                        Tổ chức, chia sẻ và bảo mật file của bạn với công nghệ AI tiên tiến. Trải nghiệm quản lý file thế hệ mới.
                    </p>
                    
                    <div class="flex flex-col sm:flex-row gap-4 mb-8">
                        <button class="bg-white text-purple-600 px-8 py-4 rounded-full font-semibold text-lg hover:bg-purple-50 transition-all pulse-glow">
                            <i class="fas fa-rocket mr-2"></i>
                            Bắt đầu miễn phí
                        </button>
                        <button class="border-2 border-white text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-white hover:text-purple-600 transition-all">
                            <i class="fas fa-play mr-2"></i>
                            Xem demo
                        </button>
                    </div>
                    
                    <div class="flex items-center space-x-6 text-purple-200">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle mr-2"></i>
                            <span>Miễn phí 30 ngày</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-shield-alt mr-2"></i>
                            <span>Bảo mật tuyệt đối</span>
                        </div>
                    </div>
                </div>
                
                <div class="relative">
                    <div class="glass-effect rounded-3xl p-8 hover-scale">
                        <div class="bg-white rounded-2xl p-6 shadow-xl">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-800">Tài liệu gần đây</h3>
                                <i class="fas fa-ellipsis-h text-gray-400"></i>
                            </div>
                            
                            <div class="space-y-3">
                                <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors cursor-pointer">
                                    <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-file-pdf text-red-500"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium text-gray-800">Báo cáo Q4.pdf</p>
                                        <p class="text-sm text-gray-500">2.4 MB • 2 giờ trước</p>
                                    </div>
                                    <i class="fas fa-download text-gray-400 hover:text-purple-600 transition-colors"></i>
                                </div>
                                
                                <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors cursor-pointer">
                                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-file-word text-blue-500"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium text-gray-800">Hợp đồng.docx</p>
                                        <p class="text-sm text-gray-500">1.1 MB • 1 ngày trước</p>
                                    </div>
                                    <i class="fas fa-share-alt text-gray-400 hover:text-purple-600 transition-colors"></i>
                                </div>
                                
                                <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors cursor-pointer">
                                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-file-excel text-green-500"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium text-gray-800">Dữ liệu khách hàng.xlsx</p>
                                        <p class="text-sm text-gray-500">5.2 MB • 3 ngày trước</p>
                                    </div>
                                    <i class="fas fa-star text-yellow-400"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script th:src="@{/js/script.js}"></script>
  </body>
</html>
