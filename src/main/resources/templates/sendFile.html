<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Send File</title>
    <script src="https://cdn.tailwindcss.com"></script>
     <link  rel="stylesheet" th:href="@{/css/input.css}">
</head>
<body class="bg-gray-100">

<!-- Navbar -->
<nav th:replace= "~{ Fragments/header :: nav-1}" ></nav>

<!-- Main Content -->
<div class="py-10">
    <header>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 class="text-3xl font-bold leading-tight text-gray-900">
                Send File
            </h1>
        </div>
    </header>
    <main>
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="mt-8">
                <div class="w-full max-w-lg p-8 space-y-6 bg-white rounded-lg shadow-md">
                    <p class="text-sm text-gray-600">You are sending the file: <span class="font-semibold text-gray-800" th:text="${file.fileName}"></span></p>
                    <form method="post" th:action="@{/files/send/{fileId}(fileId=${file.id})}" class="space-y-4">
                        <div>
                            <label for="recipientId" class="block text-sm font-medium text-gray-700">Select recipient</label>
                            <select name="recipientId" id="recipientId" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                <option th:each="user : ${users}" th:value="${user.id}" th:text="${user.username}"></option>
                            </select>
                        </div>
                        <div>
                            <button type="submit" class="w-full px-4 py-2 font-medium text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                Send File
                            </button>
                        </div>
                    </form>
                    <div th:if="${message}" class="p-4 mt-4 text-sm text-green-700 bg-green-100 rounded-lg" role="alert">
                        <p th:text="${message}"></p>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

</body>
</html>
