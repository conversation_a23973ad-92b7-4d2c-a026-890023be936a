<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Files</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link  rel="stylesheet" th:href="@{/css/input.css}">

</head>
<body class="bg-gray-50">

<!-- Navbar -->
    <th:block th:replace= "~{ Fragments/header :: nav-1}" ></th:block>

<!-- Main Content -->
<div class="py-20 ">
    <header>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 class="text-3xl font-bold leading-tight text-gray-900">
                My Files
            </h1>
        </div>
    </header>
    <main>
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Tabs -->
            <div class="mt-6 border-b border-gray-200">
                <div class="sm:flex sm:items-baseline">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">
                        File Collections
                    </h3>
                    <div class="mt-4 sm:mt-0 sm:ml-10" >
                        <nav class="-mb-px flex space-x-8" id="file-tabs">
                            <a href="#uploaded" class="border-indigo-500 text-indigo-600 whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm" aria-current="page">
                                Uploaded Files
                            </a>
                            <a href="#shared" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm">
                                Shared with Me
                            </a>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Uploaded Files Section -->
            <section id="uploaded" class="mt-8">
                <div class="sm:flex sm:items-center">
                    <div class="sm:flex-auto">
                        <h2 class="text-xl font-semibold text-gray-900">Your Uploaded Files</h2>
                        <p class="mt-2 text-sm text-gray-700">A list of all files you've uploaded to the system.</p>
                    </div>
                </div>
                
                <!-- File Cards -->
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                    <div th:each="file : ${uploadedFiles}" class="bg-white overflow-hidden shadow rounded-lg border border-gray-200 hover:shadow-md transition-shadow duration-300">
                        <div class="p-5">
                            <!-- File Type Icon -->
                            <div class="flex justify-center mb-4">
                                <!-- Document icon -->
                                <i th:if="${#strings.startsWith(file.fileType, 'text') or #strings.contains(file.fileType, 'document')}"
                                   class='bx bxs-file-doc text-5xl text-indigo-500'></i>
                                <!-- Image icon -->
                                <i th:if="${#strings.startsWith(file.fileType, 'image')}"
                                   class='bx bxs-image text-5xl text-indigo-500'></i>
                                <!-- Video icon -->
                                <i th:if="${#strings.startsWith(file.fileType, 'video')}"
                                   class='bx bxs-video text-5xl text-indigo-500'></i>
                                <!-- Audio icon -->
                                <i th:if="${#strings.startsWith(file.fileType, 'audio')}"
                                   class='bx bxs-music text-5xl text-indigo-500'></i>
                                <!-- Default file icon -->
                                <i th:unless="${#strings.startsWith(file.fileType, 'text') or #strings.contains(file.fileType, 'document') or #strings.startsWith(file.fileType, 'image') or #strings.startsWith(file.fileType, 'video') or #strings.startsWith(file.fileType, 'audio')}"
                                   class='bx bxs-file text-5xl text-indigo-500'></i>
                            </div>
                            
                            <!-- File Info -->
                            <div class="text-center">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 truncate" th:text="${file.fileName}"></h3>
                                <div class="mt-2 flex justify-center items-center">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800"
                                          th:text="${#numbers.formatDecimal(file.fileSize / 1024.0, 1, 2)} + ' KB'"></span>
                                    <span class="ml-2 text-xs text-gray-500" th:text="${file.fileType}"></span>
                                </div>
                            </div>
                            
                            <!-- Actions -->
                            <div class="mt-5 flex justify-center space-x-3">
                                <a th:href="@{/files/download/{id}(id=${file.id})}"
                                   class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    <i class='bx bx-download mr-1'></i> Download
                                </a>
                                <a th:href="@{/files/send/{id}(id=${file.id})}"
                                   class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                    <i class='bx bx-share-alt mr-1'></i> Share
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Empty State -->
                    <div th:if="${#lists.isEmpty(uploadedFiles)}" class="col-span-full py-12 flex flex-col items-center justify-center bg-white rounded-lg border border-gray-200">
                        <i class='bx bx-cloud-upload text-6xl text-gray-400'></i>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No uploaded files</h3>
                        <p class="mt-1 text-sm text-gray-500">Get started by uploading your first file.</p>
                        <div class="mt-6">
                            <a href="/files/dashboard" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <i class='bx bx-upload mr-2'></i> Upload a File
                            </a>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Received Files Section -->
            <section id="shared" class="mt-16">
                <div class="sm:flex sm:items-center">
                    <div class="sm:flex-auto">
                        <h2 class="text-xl font-semibold text-gray-900">Files Shared with You</h2>
                        <p class="mt-2 text-sm text-gray-700">Files that other users have shared with you.</p>
                    </div>
                </div>
                
                <!-- Shared Files List -->
                <div class="mt-6 bg-white shadow overflow-hidden sm:rounded-lg border border-gray-200">
                    <ul role="list" class="divide-y divide-gray-200">
                        <li th:each="file : ${receivedFiles}" class="px-4 py-4 sm:px-6 hover:bg-gray-50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <!-- File Type Icon -->
                                    <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-lg bg-indigo-50 text-indigo-600">
                                        <!-- Document icon -->
                                        <i th:if="${#strings.startsWith(file.fileType, 'text') or #strings.contains(file.fileType, 'document')}"
                                           class='bx bxs-file-doc text-xl'></i>
                                        <!-- Image icon -->
                                        <i th:if="${#strings.startsWith(file.fileType, 'image')}"
                                           class='bx bxs-image text-xl'></i>
                                        <!-- Video icon -->
                                        <i th:if="${#strings.startsWith(file.fileType, 'video')}"
                                           class='bx bxs-video text-xl'></i>
                                        <!-- Audio icon -->
                                        <i th:if="${#strings.startsWith(file.fileType, 'audio')}"
                                           class='bx bxs-music text-xl'></i>
                                        <!-- Default file icon -->
                                        <i th:unless="${#strings.startsWith(file.fileType, 'text') or #strings.contains(file.fileType, 'document') or #strings.startsWith(file.fileType, 'image') or #strings.startsWith(file.fileType, 'video') or #strings.startsWith(file.fileType, 'audio')}"
                                           class='bx bxs-file text-xl'></i>
                                    </div>
                                    
                                    <!-- File Info -->
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-indigo-600" th:text="${file.fileName}"></div>
                                        <div class="text-sm text-gray-500">
                                            <span th:text="${file.fileType}"></span>
                                            <span class="mx-1">•</span>
                                            <span th:text="${#numbers.formatDecimal(file.fileSize / 1024.0, 1, 2)} + ' KB'"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="flex items-center">
                                    <!-- Shared By -->
                                    <div class="mr-6 text-sm text-gray-500">
                                        <span class="font-medium">Shared by:</span>
                                        <span th:text="${file.uploader.username}" class="ml-1"></span>
                                    </div>
                                    
                                    <!-- Download Button -->
                                    <a th:href="@{/files/download/{id}(id=${file.id})}"
                                       class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        <i class='bx bx-download mr-1'></i> Download
                                    </a>
                                </div>
                            </div>
                        </li>
                        
                        <!-- Empty State -->
                        <li th:if="${#lists.isEmpty(receivedFiles)}" class="px-4 py-12 flex flex-col items-center justify-center">
                            <i class='bx bx-share-alt text-6xl text-gray-400'></i>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No shared files</h3>
                            <p class="mt-1 text-sm text-gray-500">No files have been shared with you yet.</p>
                        </li>
                    </ul>
                </div>
            </section>
        </div>
    </main>
</div>


<script th:src="@{/js/script.js}" ></script>
<!-- Simple Tab Functionality -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
    const tabContainer = document.querySelector('#file-tabs');
    const tabs = tabContainer.querySelectorAll('a');
    const sections = document.querySelectorAll('section');

    tabs.forEach(tab => {
        tab.addEventListener('click', function (e) {
            e.preventDefault(); // chỉ chặn hành vi ở các tab
            // Remove active class from all tabs
            tabs.forEach(t => {
                t.classList.remove('border-indigo-500', 'text-indigo-600');
                t.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
            });

            // Add active class to clicked tab
            this.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
            this.classList.add('border-indigo-500', 'text-indigo-600');

            // Show corresponding section
            const targetId = this.getAttribute('href').substring(1);
            sections.forEach(section => {
                if (section.id === targetId) {
                    section.classList.remove('hidden');
                } else {
                    section.classList.add('hidden');
                }
            });
        });
    });

    // Initialize - show first tab content, hide others
    sections.forEach((section, index) => {
        if (index !== 0) {
            section.classList.add('hidden');
        }
    });
});

</script>

</body>
</html>
