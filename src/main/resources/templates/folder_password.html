<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Folder Password</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" th:href="@{/css/input.css}">
</head>
<body class="bg-gray-50">

<!-- Navbar -->
<nav th:replace="~{Fragments/header :: nav-1}"></nav>

<!-- Main Content -->
<div class="py-20">
    <main class="max-w-md mx-auto sm:px-6 lg:px-8">
        <div class="bg-white shadow-md rounded-lg p-8">
            <div class="text-center mb-6">
                <svg class="mx-auto h-16 w-16 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                </svg>
                <h2 class="mt-4 text-2xl font-bold text-gray-900">Password Protected Folder</h2>
                <p class="mt-2 text-gray-600">
                    The folder "<span th:text="${passwordProtectedFolder.name}" class="font-semibold"></span>" is password protected.
                    Please enter the password to access its contents.
                </p>
            </div>

            <div th:if="${error}" class="mb-4 p-4 text-sm text-red-700 bg-red-100 rounded-lg" role="alert">
                <p th:text="${error}"></p>
            </div>

            <form th:action="@{/files/folders/verify-password}" method="post" class="space-y-6">
                <input type="hidden" name="folderId" th:value="${passwordProtectedFolder.id}">
                
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                    <div class="mt-1">
                        <input id="password" name="password" type="password"
                               class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <a th:href="${parentFolder != null ? '/files/dashboard?folderId=' + parentFolder.id : '/files/dashboard'}" 
                       class="text-sm font-medium text-indigo-600 hover:text-indigo-500">
                        Back to previous folder
                    </a>
                    <button type="submit"
                            class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Submit
                    </button>
                </div>
            </form>
        </div>
    </main>
</div>

</body>
</html>